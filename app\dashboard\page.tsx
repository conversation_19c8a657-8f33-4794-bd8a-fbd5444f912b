'use client';

import { useAppDispatch, useAppSelector } from '@/store';
import type { AppDispatch } from '@/store';
import { getProfile } from '@/store/slices/authSlice';
import { useEmployeeDomainGuard } from '@/hooks/useEmployeeDomainGuard';
import Loader from '@/components/ui/Loader';
import CandidateDashboard from '@/components/dashboard/CandidateDashboard';
import EmployeeDashboard from '@/components/dashboard/EmployeeDashboard';
import { UserActivityTracker } from '@/components/UserActivityTracker';
import { useEffect } from 'react';

export default function DashboardPage() {
  const dispatch: AppDispatch = useAppDispatch();
  const user = useAppSelector((state: any) => state.auth.user);
  const isLoading = useAppSelector((state: any) => state.auth.isLoading);
  const error = useAppSelector((state: any) => state.auth.error);
  const { checkDomainStatus } = useEmployeeDomainGuard();
  // Check domain status on mount
  useEffect(() => {
    if (user) {
      checkDomainStatus();
    } else {
      dispatch(getProfile());
    }
    // Only depend on user and dispatch to avoid infinite loop
  }, [user, dispatch]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen text-red-500">
        Failed to load profile.
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen text-red-500">
        Unauthorized
      </div>
    );
  }

  const roleName = (typeof window !== 'undefined' && localStorage.getItem('roleName')) || user.roleName || user.role?.name || '';
  if (roleName.toLowerCase() === 'candidate') {
    return (
      <UserActivityTracker pageName="Candidate Dashboard">
        <CandidateDashboard user={user} />
      </UserActivityTracker>
    );
  }
  if (roleName.toLowerCase() === 'employee') {
    return (
      <UserActivityTracker pageName="Employee Dashboard">
        <EmployeeDashboard user={user} />
      </UserActivityTracker>
    );
  }
  return (
    <UserActivityTracker pageName="Unknown Role Dashboard">
      <div className="flex items-center justify-center min-h-screen text-red-500">
        Unknown role
      </div>
    </UserActivityTracker>
  );
}
