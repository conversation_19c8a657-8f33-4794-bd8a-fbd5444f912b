import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import type {
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
} from '@/types/auth';
import type { User } from '@/types/user';

import api from '@/lib/api';

const BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4000';

export const login = createAsyncThunk<AuthResponse, LoginRequest>(
  'auth/login',
  async (body, { dispatch, rejectWithValue }) => {
    try {
      const { data } = await api.post(`${BASE_URL}/auth/login`, body, {
        headers: { 'Content-Type': 'application/json' },
        withCredentials: true,
      });
      // Set token BEFORE fetching user-details
      const token = data?.data?.access_token || data?.access_token || (data?.data && data.data.token) || null;
      if (token && typeof window !== 'undefined') {
        localStorage.setItem('authToken', token);
        console.log('authToken set:', token);
      } else {
        console.warn('No authToken found in response:', data);
      }
      // Now token is available for getProfile
      await dispatch(getProfile());
      return data;
    } catch (err: any) {
      return rejectWithValue(
        err?.response?.data?.message || err?.message || 'Something went wrong'
      );
    }
  }
);

export const register = createAsyncThunk<AuthResponse, RegisterRequest>(
  'auth/register',
  async (body, { dispatch, rejectWithValue }) => {
    try {
      const { data } = await api.post(`${BASE_URL}/auth/register`, body, {
        headers: { 'Content-Type': 'application/json' },
        withCredentials: true,
      });
      // Fetch user details after register
      await dispatch(getProfile());
      return data;
    } catch (err: any) {
      return rejectWithValue(
        err?.response?.data?.message || err?.message || 'Something went wrong'
      );
    }
  }
);

export const googleSignup = createAsyncThunk<
  AuthResponse,
  { role: string; idToken: string }
>('auth/google/signup', async (body, { rejectWithValue }) => {
  try {
    const { data } = await api.post(`${BASE_URL}/auth/google/signup`, body, {
      headers: { 'Content-Type': 'application/json' },
      withCredentials: true,
    });
    return data;
  } catch (err: any) {
    return rejectWithValue(
      err?.response?.data?.message || err?.message || 'Something went wrong'
    );
  }
});

export const googleSignin = createAsyncThunk<
  AuthResponse,
  { idToken: string }
>('auth/google/signin', async (body, { rejectWithValue }) => {
  try {
    const { data } = await api.post(`${BASE_URL}/auth/google/signin`, body, {
      headers: { 'Content-Type': 'application/json' },
      withCredentials: true,
    });
    return data;
  } catch (err: any) {
    return rejectWithValue(
      err?.response?.data?.message || err?.message || 'Something went wrong'
    );
  }
});

// Keep the old method for backward compatibility
export const getGoogleResponse = googleSignup;

export const forgotPassword = createAsyncThunk<
  ForgotPasswordResponse,
  ForgotPasswordRequest
>('auth/forgotPassword', async (body, { rejectWithValue }) => {
  try {
    const { data } = await api.post(`${BASE_URL}/auth/forgot-password`, body, {
      headers: { 'Content-Type': 'application/json' },
    });
    return data;
  } catch (err: any) {
    return rejectWithValue(
      err?.response?.data?.message || err?.message || 'Something went wrong'
    );
  }
});

export const resetPassword = createAsyncThunk<
  ResetPasswordResponse,
  ResetPasswordRequest
>('auth/resetPassword', async (body, { rejectWithValue }) => {
  try {
    const { data } = await api.post(`${BASE_URL}/auth/reset-password`, body, {
      headers: { 'Content-Type': 'application/json' },
    });
    return data;
  } catch (err: any) {
    return rejectWithValue(
      err?.response?.data?.message || err?.message || 'Something went wrong'
    );
  }
});

export const getProfile = createAsyncThunk<
  { message: string; data: { user: User; image: string } },
  void
>('auth/getProfile', async (_, { rejectWithValue, getState }) => {
  try {
    // Check if we already have user data and avoid redundant calls
    const state = getState() as any;
    if (state.auth.user && !state.auth.error) {
      console.log('getProfile: User already exists, skipping API call');
      return { message: 'User already loaded', data: { user: state.auth.user, image: state.auth.user.image || null } };
    }

    // Check if there's no token, don't make the call
    const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
    if (!token) {
      console.log('getProfile: No auth token found');
      return rejectWithValue('No authentication token found');
    }

    console.log('getProfile: Making API call to fetch user details');
    const { data } = await api.get(`${BASE_URL}/auth/user-details`, {
      withCredentials: true,
    });
    return data;
  } catch (err: any) {
    console.error('getProfile error:', err?.response?.data || err?.message);
    return rejectWithValue(
      err?.response?.data?.message || err?.message || 'Something went wrong'
    );
  }
}, {
  condition: (_, { getState }) => {
    // Prevent multiple simultaneous calls
    const state = getState() as any;
    return !state.auth.isLoading;
  }
});

export const getUserById = createAsyncThunk<User, string>(
  'auth/getUserById',
  async (id, { rejectWithValue }) => {
    try {
      const { data } = await api.get(`${BASE_URL}/users/${id}`, {
        withCredentials: true,
      });
      return data.data;
    } catch (err: any) {
      return rejectWithValue(
        err?.response?.data?.message || err?.message || 'Something went wrong'
      );
    }
  }
);

export const logout = createAsyncThunk<{ message: string }, void>(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      const { data } = await api.post(`${BASE_URL}/auth/logout`, {}, {
        withCredentials: true,
      });
      return data;
    } catch (err: any) {
      return rejectWithValue(
        err?.response?.data?.message || err?.message || 'Something went wrong'
      );
    }
  }
);

interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  isLoading: false,
  error: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearAuthError(state) {
      state.error = null;
    },
    setUser(state, action: PayloadAction<User | null>) {
      state.user = action.payload;
    },
    clearAuthState(state) {
      state.user = null;
      state.isLoading = false;
      state.error = null;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(login.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, state => {
        state.isLoading = false;
        // Do not set user here! User will be set from getProfile.fulfilled
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(register.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, state => {
        state.isLoading = false;
        // Do not set user here! User will be set from getProfile.fulfilled
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(forgotPassword.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(forgotPassword.fulfilled, state => {
        state.isLoading = false;
      })
      .addCase(forgotPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(resetPassword.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resetPassword.fulfilled, state => {
        state.isLoading = false;
      })
      .addCase(resetPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(getProfile.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.data.user;
        if (action.payload.data && action.payload.data.user) {
          const roleName = action.payload.data.user.role?.name || '';
          const fullName = `${action.payload.data.user.firstName || ''} ${action.payload.data.user.lastName || ''}`.trim();
          if (typeof window !== 'undefined') {
            localStorage.setItem('roleName', roleName);
            localStorage.setItem('fullName', fullName);
          }
        }
      })
      .addCase(getProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(getUserById.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getUserById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
      })
      .addCase(getUserById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(googleSignup.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(googleSignup.fulfilled, state => {
        state.isLoading = false;
        // Do not set user here! User will be set from getProfile.fulfilled
      })
      .addCase(googleSignup.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(googleSignin.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(googleSignin.fulfilled, state => {
        state.isLoading = false;
        // Do not set user here! User will be set from getProfile.fulfilled
      })
      .addCase(googleSignin.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(logout.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(logout.fulfilled, state => {
        state.isLoading = false;
        state.user = null;
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearAuthError, setUser, clearAuthState } = authSlice.actions;
export default authSlice.reducer;
