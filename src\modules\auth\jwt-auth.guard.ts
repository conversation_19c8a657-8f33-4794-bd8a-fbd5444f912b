import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext) {
    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();

    // Log authentication attempts for debugging
    console.log('JWT Auth Guard - Request URL:', request.url);
    console.log('JWT Auth Guard - Has Authorization Header:', !!request.headers.authorization);

    if (err || !user) {
      console.log('JWT Auth Guard - Authentication failed:', {
        error: err?.message,
        info: info?.message,
        hasUser: !!user
      });

      // Provide more specific error messages
      if (info?.name === 'TokenExpiredError') {
        throw new UnauthorizedException('Token has expired');
      } else if (info?.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('Invalid token');
      } else if (info?.name === 'NotBeforeError') {
        throw new UnauthorizedException('Token not active');
      } else {
        throw new UnauthorizedException('Authentication failed');
      }
    }

    return user;
  }
}
