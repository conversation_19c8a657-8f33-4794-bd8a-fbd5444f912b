'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Avatar, AvatarImage, AvatarFallback } from '../ui/avatar';
import { useAppDispatch, useAppSelector } from '@/store';
import type { AppDispatch } from '@/store';
import { logout, clearAuthState } from '@/store/slices/authSlice';
import { clearRoles } from '@/store/slices/rolesSlice';

import Image from 'next/image';
import { Button } from '@/components/ui/button';
import {
  PieChart,
  User,
  MapPin,
  FileText,
  MessageCircle,
  Search,
  Bell,
  LayoutDashboard,
  Briefcase,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { useUserActivity } from '@/hooks/useUserActivity';

function getAvatarUrl(image: string | null | undefined): string | undefined {
  if (!image) return undefined;
  if (image.startsWith('http')) return image;
  return `http://localhost:4000/uploads/profile-image/${image}`;
}

export default function Header() {
  const user = useAppSelector((state: any) => state.auth.user);
  const dispatch: AppDispatch = useAppDispatch();
  const pathname = usePathname();
  const { trackNavigation, trackButtonClick } = useUserActivity();

  const handleLogout = async () => {
    trackButtonClick('Logout', 'header');
    try {
      await dispatch(logout());
    } catch (error) {}
    localStorage.clear();
    sessionStorage.clear();
    dispatch(clearAuthState());
    dispatch(clearRoles());
    window.location.href = '/auth/login';
  };

  type NavLink = {
    href: string;
    label: string;
    icon: React.ElementType;
  };

  const candidateNav: NavLink[] = [
    { href: '/dashboard', label: 'Home', icon: PieChart },
    { href: '/candidate-profile', label: 'Profile', icon: User },
    { href: '/dream-job', label: 'Dream Job', icon: MapPin },
    { href: '/application', label: 'Applications', icon: FileText },
    { href: '/ai-assistant', label: 'AI Assistant', icon: MessageCircle },
  ];

  const employeeNav: NavLink[] = [
    { href: '/dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { href: '/post-job', label: 'Post Job', icon: Briefcase },
  ];

  function renderNav(links: NavLink[]) {
    return (
      <nav className="flex items-center gap-1 ml-8">
        {links.map(link => {
          const isActive =
            (link.href === '/' && pathname === '/') ||
            (link.href !== '/' && pathname.startsWith(link.href));

          const handleNavClick = () => {
            if (!isActive) {
              trackNavigation(pathname, link.href);
            }
          };

          const Icon = link.icon;
          return (
            <Link
              key={link.href}
              href={link.href as any}
              onClick={handleNavClick}
              className={`flex items-center gap-2 px-4 py-2 rounded-md font-medium transition-colors text-sm ${isActive ? 'bg-[#1976F6] text-white' : 'text-[#374151] hover:bg-[#F0F4FA]'}`}
            >
              <Icon
                className={`w-5 h-5 ${isActive ? 'text-white' : 'text-[#6B7280]'}`}
              />
              {link.label}
            </Link>
          );
        })}
      </nav>
    );
  }

  // Shared header layout
  function HeaderLayout({ navLinks }: { navLinks: NavLink[] }) {
    return (
      <header className="w-full flex justify-center bg-white shadow-sm z-50 h-16">
        <div className="flex w-full max-w-6xl items-center justify-between h-full px-4">
          {/* Logo and Nav */}
          <div className="flex items-center h-full">
            <Link href="/" passHref className="flex items-center h-full">
              <Image
                src="/assets/logo/TalentLoop.svg"
                alt="TalentLoop Logo"
                width={130}
                height={32}
                priority
                className="mr-2"
              />
            </Link>
            {renderNav(navLinks)}
          </div>
          {/* Right Side */}
          <div className="flex items-center gap-2 ml-4">
            <Button variant="ghost" size="icon" className="rounded-full">
              <Search className="w-5 h-5 text-[#1976F6]" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="rounded-full relative"
            >
              <Bell className="w-5 h-5 text-[#F87171]" />
              {/* Notification dot */}
              <span className="absolute top-1 right-1 w-2 h-2 bg-[#F87171] rounded-full border-2 border-white"></span>
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="cursor-pointer hover:shadow rounded-full">
                  <Avatar className="w-9 h-9 border border-[#E3F0FF] hover:border-[#1976F6] transition-colors">
                    {user?.image ? (
                      <AvatarImage
                        src={getAvatarUrl(user.image)}
                        alt="User avatar"
                      />
                    ) : (
                      <AvatarFallback className="bg-[#E3F0FF] text-[#0858F8] font-bold">
                        {user?.firstName?.[0] || 'U'}
                        {user?.lastName?.[0] || ''}
                      </AvatarFallback>
                    )}
                  </Avatar>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="rounded-xl border border-[#E3F0FF] shadow-2xl p-2 min-w-[180px] bg-white"
              >
                <DropdownMenuItem
                  disabled
                  className="font-semibold text-[#1976F6] cursor-default"
                >
                  {user?.firstName} {user?.lastName}
                </DropdownMenuItem>
                <DropdownMenuSeparator className="my-2 bg-[#E3F0FF]" />
                <DropdownMenuItem
                  onClick={handleLogout}
                  className="text-[#F87171] font-medium hover:bg-[#F87171]/10 rounded-lg transition-colors cursor-pointer"
                >
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>
    );
  }

  // Only show candidate header for authenticated candidate
  // Prefer localStorage for roleName
  const roleName = (typeof window !== 'undefined' && localStorage.getItem('roleName')) || user?.roleName || user?.role?.name || '';

  if (roleName.toLowerCase() === 'candidate') {
    return <HeaderLayout navLinks={candidateNav} />;
  }

  if (roleName.toLowerCase() === 'employee') {
    return <HeaderLayout navLinks={employeeNav} />;
  }

  return null;
}
