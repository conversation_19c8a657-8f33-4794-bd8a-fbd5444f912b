import axios from 'axios';
import Router from 'next/router';

// Prevent multiple redirects on 401
let isRedirecting = false;

// Create axios instance
const api = axios.create({
  withCredentials: true,
});

// Add a request interceptor to automatically attach Authorization header
api.interceptors.request.use(
  config => {
    // Get token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;

    // Add Authorization header if token exists and not already present
    if (token && !config.headers.Authorization) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Add a response interceptor
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response && error.response.status === 401) {
      console.log('401 detected by interceptor');
      if (typeof window !== 'undefined' && !isRedirecting) {
        if (window.location.pathname !== '/auth/login') {
          isRedirecting = true;

          // Clear all authentication data
          localStorage.removeItem('persist:root');
          localStorage.removeItem('authToken');
          localStorage.removeItem('roleName');
          localStorage.removeItem('fullName');
          sessionStorage.clear();

          // Dispatch auth clear action if store is available
          (window as any).store?.dispatch({ type: 'auth/clearAuthState' });

          // Redirect to login and reset flag after navigation
          Router.replace('/auth/login').finally(() => {
            // Reset the flag after a short delay to prevent race conditions
            setTimeout(() => {
              isRedirecting = false;
            }, 1000);
          });
        }
      }
    }
    return Promise.reject(error);
  }
);

export default api;
