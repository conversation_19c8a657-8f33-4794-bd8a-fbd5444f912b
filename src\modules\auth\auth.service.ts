import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { UserService } from '../user/user.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { MailService } from '../mailer/mailer.service';
import * as bcrypt from 'bcrypt';
import { CreateUserDto } from '../user/dto/create-user.dto';
import { GoogleIdTokenPayload, ResetPasswordPayload } from './dto/auth.dto';
import { RoleService } from '../role/role.service';
import { CandidateProfileService } from '../../modules/candidate/candidate.service';
import { CreateCandidateProfileDto } from '../../modules/candidate/dto/create-candidate-profile.dto';
import { LoginDto } from './dto/login.dto';
import { EmployerProfileService } from '../../modules/employee/employee.service';
import { ERROR } from '../../utils/error-code';
import { OAuth2Client } from 'google-auth-library';
import { User } from '../user/schemas/user.schema';
import { ROLE, TOKEN_EXPIRATION } from 'src/utils/constants';
import { Types } from 'mongoose';

@Injectable()
export class AuthService {
  constructor(
    private userService: UserService,
    private jwtService: JwtService,
    private configService: ConfigService,
    private mailService: MailService,
    private roleService: RoleService,
    private candidateProfileService: CandidateProfileService,
    private employerProfileService: EmployerProfileService, // <-- inject
  ) {}

  /**
   * User login
   */
  async login(loginDto: LoginDto): Promise<{
    message: string;
    data: { user: User; access_token: string };
    statusCode: number;
  }> {
    try {
      const user = await this.userService.findOneByEmail(loginDto.email);

      if (!user || !user.password) {
        throw new UnauthorizedException(ERROR.INVALID_CREDENTIALS);
      }

      const isPasswordValid = await bcrypt.compare(
        loginDto.password,
        user.password,
      );
      if (!isPasswordValid) {
        throw new UnauthorizedException(ERROR.INVALID_CREDENTIALS);
      }

      // Sanitize user data (avoid exposing password)
      const { ...sanitizedUser } = user;

      const payload = {
        sub: user._id,
        email: user.email,
        role: user.role,
      };

      const access_token = this.jwtService.sign(payload);

      return {
        message: ERROR.LOGIN_SUCCESS,
        data: {
          user: sanitizedUser as User,
          access_token,
        },
        statusCode: 200,
      };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }

      console.error('Login error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * User registration
   */
  async register(createUserDto: CreateUserDto): Promise<{
    message: string;
    data: { user: User; access_token: string };
    statusCode: number;
  }> {
    try {
      // 1. Check if user already exists
      const existingUser = await this.userService.findOneByEmail(
        createUserDto.email,
      );
      if (existingUser) {
        throw new BadRequestException(ERROR.USER_EXISTS);
      }

      // 2. Hash the password
      const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

      // 3. Create new user
      const result = await this.userService.create({
        ...createUserDto,
        password: hashedPassword,
      });

      if (!result.data) {
        throw new BadRequestException(
          result.message || ERROR.REGISTRATION_FAILED,
        );
      }

      const { ...user } = result.data;

      // 4. Create profile based on role
      const roleName = user.role?.['name']?.toString().toLowerCase() || '';

      if (roleName === 'employee') {
        await this.employerProfileService.createEmployerProfile(
          user._id as string,
          {
            contactEmail: user.email,
          },
        );
      } else {
        const existingProfile = await this.candidateProfileService.findByUserId(
          user._id as string,
        );
        if (!existingProfile) {
          const profileDto: CreateCandidateProfileDto = {
            fullName: `${user.firstName} ${user.lastName}`.trim(),
            email: user.email,
          };
          await this.candidateProfileService.create(
            user._id as string,
            profileDto,
          );
        }
      }

      // 5. Generate JWT
      const payload = {
        sub: user._id,
        email: user.email,
        role: user.role,
      };
      const access_token = this.jwtService.sign(payload);

      return {
        message: ERROR.REGISTRATION_SUCCESS,
        data: {
          user,
          access_token,
        },
        statusCode: 201,
      };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }

      console.error('Registration error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Send reset link via email
   */
  async forgotPassword(email: string, portalType: string) {
    try {
      const user = await this.userService.findOneByEmail(email);
      if (!user) throw new BadRequestException(ERROR.EMAIL_NOT_FOUND);
      const token = this.jwtService.sign(
        { sub: user._id },
        {
          expiresIn: TOKEN_EXPIRATION.RESET_PASSWORD,
          secret: this.configService.get('jwt.resetSecret'),
        },
      );
      let portalUrl = this.configService.get('app.frontendUrl');
      if (portalType === ROLE.ADMIN) {
        portalUrl = this.configService.get('app.adminFrontendUrl');
      }
      const resetUrl = `${portalUrl}/auth/reset-password?token=${token}`;
      await this.mailService.sendResetPasswordMail(
        user.email,
        user.firstName,
        resetUrl,
      );
      return {
        message: ERROR.PASSWORD_RESET_LINK,
        statusCode: 200,
        data: null,
      };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      )
        throw error;
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Reset password using token
   */
  async resetPassword(token: string, newPassword: string) {
    if (
      !newPassword ||
      typeof newPassword !== 'string' ||
      !newPassword.trim()
    ) {
      throw new BadRequestException('New password must be provided');
    }
    try {
      const decoded = this.jwtService.verify<ResetPasswordPayload>(token, {
        secret: this.configService.get<string>('jwt.resetSecret'),
      });

      const hashed = await bcrypt.hash(newPassword, 10);
      await this.userService.updatePasswordById(decoded.sub, hashed);

      return {
        message: 'Password updated successfully',
        statusCode: 200,
        data: null,
      };
    } catch (err: unknown) {
      console.error('Error resetting password:', err);
      throw new BadRequestException('Invalid or expired token');
    }
  }

  /**
   * Handle Google Signup - Create new user if email doesn't exist
   */
  async handleGoogleSignup(idToken: string, roleId: string) {
    try {
      const client = new OAuth2Client(
        this.configService.get<string>('google.clientId'),
      );

      const googleUser = await client.verifyIdToken({
        idToken,
        audience: this.configService.get<string>('google.clientId'),
      });

      const payload = googleUser.getPayload() as GoogleIdTokenPayload;
      const email = payload.email;

      // Check if user already exists - for signup, we don't want existing users
      const existingUser = await this.userService.findOneByEmail(email);
      if (existingUser) {
        throw new BadRequestException(ERROR.USER_EXISTS);
      }

      const role = await this.roleService.findById(roleId);
      if (!role) {
        throw new BadRequestException(ERROR.INVALID_ROLE);
      }

      const { firstName, lastName } = this.extractNameParts(payload.name);

      const newUserResult = await this.userService.create({
        email,
        firstName,
        lastName,
        role: roleId,
      });

      if (!newUserResult.data) {
        throw new Error(
          newUserResult.message || ERROR.GOOGLE_REGISTRATION_FAILED,
        );
      }

      const user = newUserResult.data;

      await this.createProfileBasedOnRole(user, payload, role.name);

      const tokenPayload = {
        sub: user._id,
        email: user.email,
        role: user.role,
      };
      const access_token = this.jwtService.sign(tokenPayload);
      return {
        message: ERROR.LOGIN_SUCCESS,
        data: {
          user,
          access_token,
        },
        statusCode: 201,
      };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }

      console.error('Google Signup Error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Handle Google Signin - Login existing user if email exists
   */
  async handleGoogleSignin(idToken: string) {
    try {
      const client = new OAuth2Client(
        this.configService.get<string>('google.clientId'),
      );

      const googleUser = await client.verifyIdToken({
        idToken,
        audience: this.configService.get<string>('google.clientId'),
      });

      const payload = googleUser.getPayload() as GoogleIdTokenPayload;
      const email = payload.email;

      const existingUser = await this.userService.findOneByEmail(email);
      if (!existingUser) {
        throw new BadRequestException(ERROR.USER_NOT_FOUND);
      }

      const { ...sanitizedUser } = existingUser;

      const tokenPayload = {
        sub: existingUser._id,
        email: existingUser.email,
        role: existingUser.role,
      };
      const access_token = this.jwtService.sign(tokenPayload);

      return {
        message: ERROR.LOGIN_SUCCESS,
        data: {
          user: sanitizedUser as User,
          access_token,
        },
        statusCode: 200,
      };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }

      console.error('Google Signin Error:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  private extractNameParts(fullName = ''): {
    firstName: string;
    lastName: string;
  } {
    const parts = fullName.trim().split(' ');
    return {
      firstName: parts[0] || '',
      lastName: parts.slice(1).join(' ') || '',
    };
  }

  private async createProfileBasedOnRole(
    user: User,
    googleData: GoogleIdTokenPayload,
    roleName: string,
  ) {
    const userId = user._id as string;

    if (roleName.toLowerCase() === 'employee') {
      await this.employerProfileService.createEmployerProfile(userId, {
        companyName: 'Company Name', // Ideally from payload or default
        contactEmail: user.email,
      });
    } else {
      const profileExists =
        await this.candidateProfileService.findByUserId(userId);
      if (!profileExists) {
        const profileDto: CreateCandidateProfileDto = {
          fullName: `${user.firstName} ${user.lastName}`.trim(),
          email: googleData.email,
          image: googleData.picture,
        };
        await this.candidateProfileService.create(userId, profileDto);
      }
    }
  }

  async getUserImage(userId: string): Promise<string | null> {
    try {
      return this.candidateProfileService.findImageByUserId(userId);
    } catch (error) {
      console.error('Error fetching user image:', error);
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async adminLogin(loginDto: LoginDto): Promise<{
    message: string;
    data: { user: User; access_token: string };
    statusCode: number;
  }> {
    try {
      const user = await this.userService.findOneByEmail(loginDto.email);

      // 1. Check user and password presence
      if (!user || !user.password) {
        throw new UnauthorizedException(ERROR.INVALID_CREDENTIALS);
      }

      // 2. Validate password
      const isPasswordValid = await bcrypt.compare(
        loginDto.password,
        user.password,
      );
      if (!isPasswordValid) {
        throw new UnauthorizedException(ERROR.INVALID_CREDENTIALS);
      }

      // 3. Validate role
      const roleName = user.role?.['name']?.toString().toLowerCase() || '';
      if (roleName !== ROLE.ADMIN) {
        throw new UnauthorizedException(ERROR.ONLY_ADMIN_LOGIN);
      }

      // 4. Remove sensitive fields from user object
      const { ...sanitizedUser } = user;

      // 5. Generate JWT
      const tokenPayload = {
        sub: user._id,
        email: user.email,
        role: user.role,
      };
      const access_token = this.jwtService.sign(tokenPayload);

      return {
        message: ERROR.ADMIN_LOGIN_SUCCESS,
        data: {
          user: sanitizedUser as User,
          access_token,
        },
        statusCode: 200, // Added status code for consistency
      };
    } catch (error) {
      // Standardized error wrapping
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }

      console.error('Admin login error:', error); // Optional: log internal error for auditing
      throw new BadRequestException(ERROR.INTERNAL_SERVER_ERROR);
    }
  }

  async userDetails(
    userId: string,
  ): Promise<{ user: User; image: string | null }> {
    const user = await this.userService.getUserById(userId);

    if (!user) {
      throw new NotFoundException(ERROR.USER_NOT_FOUND);
    }

    const image = await this.getUserImage(
      (user._id as Types.ObjectId).toString(),
    );

    return { user, image };
  }
}
